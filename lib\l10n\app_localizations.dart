import 'dart:async';

import 'package:flutter/foundation.dart';
import 'package:flutter/widgets.dart';
import 'package:flutter_localizations/flutter_localizations.dart';
import 'package:intl/intl.dart' as intl;

import 'app_localizations_ar.dart';
import 'app_localizations_en.dart';
import 'app_localizations_fr.dart';

// ignore_for_file: type=lint

/// Callers can lookup localized strings with an instance of AppLocalizations
/// returned by `AppLocalizations.of(context)`.
///
/// Applications need to include `AppLocalizations.delegate()` in their app's
/// `localizationDelegates` list, and the locales they support in the app's
/// `supportedLocales` list. For example:
///
/// ```dart
/// import 'l10n/app_localizations.dart';
///
/// return MaterialApp(
///   localizationsDelegates: AppLocalizations.localizationsDelegates,
///   supportedLocales: AppLocalizations.supportedLocales,
///   home: MyApplicationHome(),
/// );
/// ```
///
/// ## Update pubspec.yaml
///
/// Please make sure to update your pubspec.yaml to include the following
/// packages:
///
/// ```yaml
/// dependencies:
///   # Internationalization support.
///   flutter_localizations:
///     sdk: flutter
///   intl: any # Use the pinned version from flutter_localizations
///
///   # Rest of dependencies
/// ```
///
/// ## iOS Applications
///
/// iOS applications define key application metadata, including supported
/// locales, in an Info.plist file that is built into the application bundle.
/// To configure the locales supported by your app, you’ll need to edit this
/// file.
///
/// First, open your project’s ios/Runner.xcworkspace Xcode workspace file.
/// Then, in the Project Navigator, open the Info.plist file under the Runner
/// project’s Runner folder.
///
/// Next, select the Information Property List item, select Add Item from the
/// Editor menu, then select Localizations from the pop-up menu.
///
/// Select and expand the newly-created Localizations item then, for each
/// locale your application supports, add a new item and select the locale
/// you wish to add from the pop-up menu in the Value field. This list should
/// be consistent with the languages listed in the AppLocalizations.supportedLocales
/// property.
abstract class AppLocalizations {
  AppLocalizations(String locale) : localeName = intl.Intl.canonicalizedLocale(locale.toString());

  final String localeName;

  static AppLocalizations of(BuildContext context) {
    return Localizations.of<AppLocalizations>(context, AppLocalizations)!;
  }

  static const LocalizationsDelegate<AppLocalizations> delegate = _AppLocalizationsDelegate();

  /// A list of this localizations delegate along with the default localizations
  /// delegates.
  ///
  /// Returns a list of localizations delegates containing this delegate along with
  /// GlobalMaterialLocalizations.delegate, GlobalCupertinoLocalizations.delegate,
  /// and GlobalWidgetsLocalizations.delegate.
  ///
  /// Additional delegates can be added by appending to this list in
  /// MaterialApp. This list does not have to be used at all if a custom list
  /// of delegates is preferred or required.
  static const List<LocalizationsDelegate<dynamic>> localizationsDelegates = <LocalizationsDelegate<dynamic>>[
    delegate,
    GlobalMaterialLocalizations.delegate,
    GlobalCupertinoLocalizations.delegate,
    GlobalWidgetsLocalizations.delegate,
  ];

  /// A list of this localizations delegate's supported locales.
  static const List<Locale> supportedLocales = <Locale>[
    Locale('ar'),
    Locale('en'),
    Locale('fr')
  ];

  /// No description provided for @appTitle.
  ///
  /// In en, this message translates to:
  /// **'CCP RIP Calculator'**
  String get appTitle;

  /// No description provided for @bottomNavCalculate.
  ///
  /// In en, this message translates to:
  /// **'Calculate'**
  String get bottomNavCalculate;

  /// No description provided for @bottomNavAccounts.
  ///
  /// In en, this message translates to:
  /// **'Accounts'**
  String get bottomNavAccounts;

  /// No description provided for @bottomNavVerify.
  ///
  /// In en, this message translates to:
  /// **'Verify'**
  String get bottomNavVerify;

  /// No description provided for @homeScreenTitle.
  ///
  /// In en, this message translates to:
  /// **'CCP RIP Calculator'**
  String get homeScreenTitle;

  /// No description provided for @savedAccountsScreenTitle.
  ///
  /// In en, this message translates to:
  /// **'Saved Accounts'**
  String get savedAccountsScreenTitle;

  /// No description provided for @verifyRipScreenTitle.
  ///
  /// In en, this message translates to:
  /// **'Verify RIP Account'**
  String get verifyRipScreenTitle;

  /// No description provided for @menuPrivacyPolicy.
  ///
  /// In en, this message translates to:
  /// **'Privacy Policy'**
  String get menuPrivacyPolicy;

  /// No description provided for @menuRateApp.
  ///
  /// In en, this message translates to:
  /// **'Rate App'**
  String get menuRateApp;

  /// No description provided for @menuUpdate.
  ///
  /// In en, this message translates to:
  /// **'Update'**
  String get menuUpdate;

  /// No description provided for @menuAbout.
  ///
  /// In en, this message translates to:
  /// **'About'**
  String get menuAbout;

  /// No description provided for @menuQuit.
  ///
  /// In en, this message translates to:
  /// **'Quit'**
  String get menuQuit;

  /// No description provided for @menuCloudBackup.
  ///
  /// In en, this message translates to:
  /// **'Cloud Backup'**
  String get menuCloudBackup;

  /// No description provided for @menuLocalBackup.
  ///
  /// In en, this message translates to:
  /// **'Local Backup'**
  String get menuLocalBackup;

  /// No description provided for @cloudBackupTitle.
  ///
  /// In en, this message translates to:
  /// **'Cloud Backup'**
  String get cloudBackupTitle;

  /// No description provided for @cloudBackupInfo.
  ///
  /// In en, this message translates to:
  /// **'About Cloud Backup'**
  String get cloudBackupInfo;

  /// No description provided for @cloudBackupDescription.
  ///
  /// In en, this message translates to:
  /// **'Cloud backup allows you to save your CCP accounts to Google Drive. This way, you can restore your accounts if you change devices or reinstall the app.'**
  String get cloudBackupDescription;

  /// No description provided for @cloudBackupPrivacy.
  ///
  /// In en, this message translates to:
  /// **'Your data is stored privately in your Google Drive account. Only you have access to it.'**
  String get cloudBackupPrivacy;

  /// No description provided for @cloudBackupSignInRequired.
  ///
  /// In en, this message translates to:
  /// **'Sign in to Google Drive to use cloud backup'**
  String get cloudBackupSignInRequired;

  /// No description provided for @cloudBackupSignIn.
  ///
  /// In en, this message translates to:
  /// **'Sign in with Google'**
  String get cloudBackupSignIn;

  /// No description provided for @cloudBackupSignOut.
  ///
  /// In en, this message translates to:
  /// **'Sign out'**
  String get cloudBackupSignOut;

  /// No description provided for @cloudBackupLastBackup.
  ///
  /// In en, this message translates to:
  /// **'Last backup'**
  String get cloudBackupLastBackup;

  /// No description provided for @cloudBackupBackup.
  ///
  /// In en, this message translates to:
  /// **'Backup'**
  String get cloudBackupBackup;

  /// No description provided for @cloudBackupRestore.
  ///
  /// In en, this message translates to:
  /// **'Restore'**
  String get cloudBackupRestore;

  /// No description provided for @cloudBackupSuccess.
  ///
  /// In en, this message translates to:
  /// **'Accounts backed up successfully'**
  String get cloudBackupSuccess;

  /// No description provided for @cloudBackupRestoreSuccess.
  ///
  /// In en, this message translates to:
  /// **'Accounts restored successfully'**
  String get cloudBackupRestoreSuccess;

  /// No description provided for @cloudBackupRestoreConfirmTitle.
  ///
  /// In en, this message translates to:
  /// **'Restore Accounts'**
  String get cloudBackupRestoreConfirmTitle;

  /// No description provided for @cloudBackupRestoreConfirmMessage.
  ///
  /// In en, this message translates to:
  /// **'This will replace your current accounts with the ones from your last backup. Continue?'**
  String get cloudBackupRestoreConfirmMessage;

  /// No description provided for @cancel.
  ///
  /// In en, this message translates to:
  /// **'Cancel'**
  String get cancel;

  /// No description provided for @confirm.
  ///
  /// In en, this message translates to:
  /// **'Confirm'**
  String get confirm;

  /// No description provided for @localBackupTitle.
  ///
  /// In en, this message translates to:
  /// **'Local Backup'**
  String get localBackupTitle;

  /// No description provided for @localBackupExport.
  ///
  /// In en, this message translates to:
  /// **'Export Accounts'**
  String get localBackupExport;

  /// No description provided for @localBackupExportDescription.
  ///
  /// In en, this message translates to:
  /// **'Export your accounts to a file that you can save on your device or share with other apps.'**
  String get localBackupExportDescription;

  /// No description provided for @localBackupExportButton.
  ///
  /// In en, this message translates to:
  /// **'Export'**
  String get localBackupExportButton;

  /// No description provided for @localBackupExportSuccess.
  ///
  /// In en, this message translates to:
  /// **'Accounts exported successfully'**
  String get localBackupExportSuccess;

  /// No description provided for @localBackupExportError.
  ///
  /// In en, this message translates to:
  /// **'Failed to export accounts'**
  String get localBackupExportError;

  /// No description provided for @localBackupImport.
  ///
  /// In en, this message translates to:
  /// **'Import Accounts'**
  String get localBackupImport;

  /// No description provided for @localBackupImportDescription.
  ///
  /// In en, this message translates to:
  /// **'Import accounts from a backup file. Paste the JSON content below:'**
  String get localBackupImportDescription;

  /// No description provided for @localBackupImportHint.
  ///
  /// In en, this message translates to:
  /// **'Paste JSON content here'**
  String get localBackupImportHint;

  /// No description provided for @localBackupImportButton.
  ///
  /// In en, this message translates to:
  /// **'Import'**
  String get localBackupImportButton;

  /// No description provided for @localBackupImportConfirmTitle.
  ///
  /// In en, this message translates to:
  /// **'Import Accounts'**
  String get localBackupImportConfirmTitle;

  /// No description provided for @localBackupImportConfirmMessage.
  ///
  /// In en, this message translates to:
  /// **'This will add the accounts from the backup to your existing accounts. Continue?'**
  String get localBackupImportConfirmMessage;

  /// No description provided for @localBackupImportSuccess.
  ///
  /// In en, this message translates to:
  /// **'Successfully imported'**
  String get localBackupImportSuccess;

  /// No description provided for @localBackupImportNoAccounts.
  ///
  /// In en, this message translates to:
  /// **'No new accounts were imported'**
  String get localBackupImportNoAccounts;

  /// No description provided for @localBackupImportError.
  ///
  /// In en, this message translates to:
  /// **'Failed to import accounts'**
  String get localBackupImportError;

  /// No description provided for @localBackupNote.
  ///
  /// In en, this message translates to:
  /// **'Note: Importing accounts will not delete your existing accounts. New accounts will be added to your existing ones.'**
  String get localBackupNote;

  /// No description provided for @verifyRipScannerTooltip.
  ///
  /// In en, this message translates to:
  /// **'Scan RIP Code'**
  String get verifyRipScannerTooltip;

  /// No description provided for @changeLanguageTooltip.
  ///
  /// In en, this message translates to:
  /// **'Change Language'**
  String get changeLanguageTooltip;

  /// No description provided for @verifyRipHeaderTitle.
  ///
  /// In en, this message translates to:
  /// **'RIP Account Verification'**
  String get verifyRipHeaderTitle;

  /// No description provided for @verifyRipHeaderSubtitle.
  ///
  /// In en, this message translates to:
  /// **'Enter an RIP code to check its validity'**
  String get verifyRipHeaderSubtitle;

  /// No description provided for @verifyRipInputLabel.
  ///
  /// In en, this message translates to:
  /// **'RIP Account'**
  String get verifyRipInputLabel;

  /// No description provided for @verifyRipInputHint.
  ///
  /// In en, this message translates to:
  /// **'Format: ********xxxxxxxxxxxx'**
  String get verifyRipInputHint;

  /// No description provided for @verifyRipPasteTooltip.
  ///
  /// In en, this message translates to:
  /// **'Paste from clipboard'**
  String get verifyRipPasteTooltip;

  /// No description provided for @verifyRipButtonText.
  ///
  /// In en, this message translates to:
  /// **'VERIFY'**
  String get verifyRipButtonText;

  /// No description provided for @verifyRipValidationEmpty.
  ///
  /// In en, this message translates to:
  /// **'Please enter an RIP account'**
  String get verifyRipValidationEmpty;

  /// No description provided for @verifyRipValidationLength.
  ///
  /// In en, this message translates to:
  /// **'RIP code must be exactly 20 digits'**
  String get verifyRipValidationLength;

  /// No description provided for @verifyRipValidationPrefix.
  ///
  /// In en, this message translates to:
  /// **'RIP code must start with ********'**
  String get verifyRipValidationPrefix;

  /// No description provided for @verifyRipResultValid.
  ///
  /// In en, this message translates to:
  /// **'Valid RIP Account'**
  String get verifyRipResultValid;

  /// No description provided for @verifyRipResultInvalid.
  ///
  /// In en, this message translates to:
  /// **'Invalid RIP Account'**
  String get verifyRipResultInvalid;

  /// No description provided for @verifyRipResultValidSub.
  ///
  /// In en, this message translates to:
  /// **'The RIP code was successfully verified'**
  String get verifyRipResultValidSub;

  /// No description provided for @verifyRipResultInvalidSub.
  ///
  /// In en, this message translates to:
  /// **'The entered RIP code is incorrect'**
  String get verifyRipResultInvalidSub;

  /// No description provided for @verifyRipInfoTitle.
  ///
  /// In en, this message translates to:
  /// **'Account Information'**
  String get verifyRipInfoTitle;

  /// No description provided for @verifyRipInfoPrefixLabel.
  ///
  /// In en, this message translates to:
  /// **'Prefix'**
  String get verifyRipInfoPrefixLabel;

  /// No description provided for @verifyRipInfoBankCodeLabel.
  ///
  /// In en, this message translates to:
  /// **'Bank Code'**
  String get verifyRipInfoBankCodeLabel;

  /// No description provided for @verifyRipInfoCcpNumberLabel.
  ///
  /// In en, this message translates to:
  /// **'CCP Number'**
  String get verifyRipInfoCcpNumberLabel;

  /// No description provided for @verifyRipInfoCcpKeyLabel.
  ///
  /// In en, this message translates to:
  /// **'CCP Key'**
  String get verifyRipInfoCcpKeyLabel;

  /// No description provided for @verifyRipInfoRipKeyLabel.
  ///
  /// In en, this message translates to:
  /// **'RIP Key'**
  String get verifyRipInfoRipKeyLabel;

  /// No description provided for @verifyRipFullRipLabel.
  ///
  /// In en, this message translates to:
  /// **'RIP Account'**
  String get verifyRipFullRipLabel;

  /// No description provided for @verifyRipCopyButton.
  ///
  /// In en, this message translates to:
  /// **'Copy'**
  String get verifyRipCopyButton;

  /// No description provided for @verifyRipCopiedMessage.
  ///
  /// In en, this message translates to:
  /// **'RIP Account copied'**
  String get verifyRipCopiedMessage;

  /// No description provided for @verifyRipGenericInvalidMessage.
  ///
  /// In en, this message translates to:
  /// **'The entered RIP account is incorrect. Please check and try again.'**
  String get verifyRipGenericInvalidMessage;

  /// No description provided for @changeLanguageDialogTitle.
  ///
  /// In en, this message translates to:
  /// **'Change Language'**
  String get changeLanguageDialogTitle;

  /// No description provided for @ccpFormInputLabel.
  ///
  /// In en, this message translates to:
  /// **'CCP Number'**
  String get ccpFormInputLabel;

  /// No description provided for @ccpFormInputHint.
  ///
  /// In en, this message translates to:
  /// **'Enter CCP number'**
  String get ccpFormInputHint;

  /// No description provided for @ccpFormValidationEmpty.
  ///
  /// In en, this message translates to:
  /// **'Please enter a CCP number'**
  String get ccpFormValidationEmpty;

  /// No description provided for @ccpFormValidationInvalid.
  ///
  /// In en, this message translates to:
  /// **'Please enter a valid CCP number'**
  String get ccpFormValidationInvalid;

  /// No description provided for @ccpFormResultTitle.
  ///
  /// In en, this message translates to:
  /// **'Calculation successful'**
  String get ccpFormResultTitle;

  /// No description provided for @ccpFormResultSubtitle.
  ///
  /// In en, this message translates to:
  /// **'Your RIP code is ready to use'**
  String get ccpFormResultSubtitle;

  /// No description provided for @ccpFormCcpKeyLabel.
  ///
  /// In en, this message translates to:
  /// **'CCP Key'**
  String get ccpFormCcpKeyLabel;

  /// No description provided for @ccpFormRipKeyLabel.
  ///
  /// In en, this message translates to:
  /// **'RIP Key'**
  String get ccpFormRipKeyLabel;

  /// No description provided for @ccpFormRipAccountLabel.
  ///
  /// In en, this message translates to:
  /// **'RIP Account'**
  String get ccpFormRipAccountLabel;

  /// No description provided for @ccpFormSaveButton.
  ///
  /// In en, this message translates to:
  /// **'SAVE ACCOUNT'**
  String get ccpFormSaveButton;

  /// No description provided for @ccpFormSaveButtonShort.
  ///
  /// In en, this message translates to:
  /// **'SAVE'**
  String get ccpFormSaveButtonShort;

  /// No description provided for @ccpFormAccountExistsError.
  ///
  /// In en, this message translates to:
  /// **'An account with this RIP code already exists'**
  String get ccpFormAccountExistsError;

  /// No description provided for @ccpFormClearTooltip.
  ///
  /// In en, this message translates to:
  /// **'Clear'**
  String get ccpFormClearTooltip;

  /// No description provided for @savedAccountsImportTooltip.
  ///
  /// In en, this message translates to:
  /// **'Import accounts'**
  String get savedAccountsImportTooltip;

  /// No description provided for @savedAccountsExportTooltip.
  ///
  /// In en, this message translates to:
  /// **'Export accounts'**
  String get savedAccountsExportTooltip;

  /// No description provided for @savedAccountsSearchLabel.
  ///
  /// In en, this message translates to:
  /// **'Search'**
  String get savedAccountsSearchLabel;

  /// No description provided for @savedAccountsSearchHint.
  ///
  /// In en, this message translates to:
  /// **'Search by name or CCP number'**
  String get savedAccountsSearchHint;

  /// No description provided for @savedAccountsEmptySearch.
  ///
  /// In en, this message translates to:
  /// **'No accounts found'**
  String get savedAccountsEmptySearch;

  /// No description provided for @savedAccountsEmpty.
  ///
  /// In en, this message translates to:
  /// **'No saved accounts'**
  String get savedAccountsEmpty;

  /// No description provided for @savedAccountsEditDialogTitle.
  ///
  /// In en, this message translates to:
  /// **'Edit Account'**
  String get savedAccountsEditDialogTitle;

  /// No description provided for @savedAccountsEditOwnerNameLabel.
  ///
  /// In en, this message translates to:
  /// **'Owner\'s name:'**
  String get savedAccountsEditOwnerNameLabel;

  /// No description provided for @savedAccountsEditOwnerNameHint.
  ///
  /// In en, this message translates to:
  /// **'Enter name'**
  String get savedAccountsEditOwnerNameHint;

  /// No description provided for @savedAccountsDialogCancel.
  ///
  /// In en, this message translates to:
  /// **'CANCEL'**
  String get savedAccountsDialogCancel;

  /// No description provided for @savedAccountsDialogSave.
  ///
  /// In en, this message translates to:
  /// **'SAVE'**
  String get savedAccountsDialogSave;

  /// Success message for importing accounts
  ///
  /// In en, this message translates to:
  /// **'{count} accounts imported successfully'**
  String savedAccountsImportSuccess(int count);

  /// Error message for importing accounts
  ///
  /// In en, this message translates to:
  /// **'Error importing accounts: {error}'**
  String savedAccountsImportError(String error);

  /// Error message for exporting accounts
  ///
  /// In en, this message translates to:
  /// **'Error exporting accounts: {error}'**
  String savedAccountsExportError(String error);

  /// Title for the manage accounts dialog
  ///
  /// In en, this message translates to:
  /// **'Manage Accounts'**
  String get savedAccountsManageTitle;

  /// Title for the import option in manage accounts dialog
  ///
  /// In en, this message translates to:
  /// **'Import Accounts'**
  String get savedAccountsImportTitle;

  /// Title for the export option in manage accounts dialog
  ///
  /// In en, this message translates to:
  /// **'Export Accounts'**
  String get savedAccountsExportTitle;

  /// No description provided for @saveAccountScreenTitle.
  ///
  /// In en, this message translates to:
  /// **'Save Account'**
  String get saveAccountScreenTitle;

  /// No description provided for @saveAccountOwnerNameHint.
  ///
  /// In en, this message translates to:
  /// **'Enter owner\'s name'**
  String get saveAccountOwnerNameHint;

  /// No description provided for @saveAccountOwnerNameValidation.
  ///
  /// In en, this message translates to:
  /// **'Please enter the owner\'s name'**
  String get saveAccountOwnerNameValidation;

  /// No description provided for @saveAccountSuccessMessage.
  ///
  /// In en, this message translates to:
  /// **'Account saved successfully'**
  String get saveAccountSuccessMessage;

  /// No description provided for @saveAccountGenericErrorPrefix.
  ///
  /// In en, this message translates to:
  /// **'Error: '**
  String get saveAccountGenericErrorPrefix;

  /// No description provided for @savedAccountLastModified.
  ///
  /// In en, this message translates to:
  /// **'Modified on:'**
  String get savedAccountLastModified;

  /// No description provided for @savedAccountCcpLabel.
  ///
  /// In en, this message translates to:
  /// **'CCP:'**
  String get savedAccountCcpLabel;

  /// No description provided for @savedAccountRipLabel.
  ///
  /// In en, this message translates to:
  /// **'RIP Account:'**
  String get savedAccountRipLabel;

  /// No description provided for @savedAccountCopyRip.
  ///
  /// In en, this message translates to:
  /// **'Copy RIP'**
  String get savedAccountCopyRip;

  /// No description provided for @savedAccountShare.
  ///
  /// In en, this message translates to:
  /// **'Share'**
  String get savedAccountShare;

  /// No description provided for @savedAccountEdit.
  ///
  /// In en, this message translates to:
  /// **'Edit'**
  String get savedAccountEdit;

  /// No description provided for @savedAccountDelete.
  ///
  /// In en, this message translates to:
  /// **'Delete'**
  String get savedAccountDelete;

  /// No description provided for @savedAccountRipCopied.
  ///
  /// In en, this message translates to:
  /// **'RIP Account copied'**
  String get savedAccountRipCopied;

  /// No description provided for @savedAccountDeleteTitle.
  ///
  /// In en, this message translates to:
  /// **'Delete'**
  String get savedAccountDeleteTitle;

  /// Confirmation message for deleting an account
  ///
  /// In en, this message translates to:
  /// **'Delete {ownerName}\'s account?'**
  String savedAccountDeleteConfirm(String ownerName);

  /// No description provided for @savedAccountQrTitle.
  ///
  /// In en, this message translates to:
  /// **'Scan QR Code'**
  String get savedAccountQrTitle;

  /// No description provided for @savedAccountQrClose.
  ///
  /// In en, this message translates to:
  /// **'Close'**
  String get savedAccountQrClose;

  /// No description provided for @savedAccountSaveButton.
  ///
  /// In en, this message translates to:
  /// **'Save Account'**
  String get savedAccountSaveButton;

  /// No description provided for @verifyRipCopyButtonText.
  ///
  /// In en, this message translates to:
  /// **'Copy'**
  String get verifyRipCopyButtonText;

  /// No description provided for @verifyRipSaveButtonText.
  ///
  /// In en, this message translates to:
  /// **'Save'**
  String get verifyRipSaveButtonText;

  /// No description provided for @ccpFormInputInstruction.
  ///
  /// In en, this message translates to:
  /// **'Enter a CCP code without key (maximum 10 digits)'**
  String get ccpFormInputInstruction;

  /// No description provided for @appInfoButtonText.
  ///
  /// In en, this message translates to:
  /// **'More information'**
  String get appInfoButtonText;

  /// No description provided for @appInfoScreenTitle.
  ///
  /// In en, this message translates to:
  /// **'About CCP RIP DZ'**
  String get appInfoScreenTitle;

  /// No description provided for @appInfoContent.
  ///
  /// In en, this message translates to:
  /// **'📄 About CCP RIP DZ\nWelcome to the CCP RIP DZ app!\nOur app provides a simple and effective solution for managing CCP accounts in Algeria. Here\'s what you can do with it:\n\n🔧 Key Features:\n\n✅ RIP Calculation: \nEasily calculate the RIP (Postal Identity Statement) from your CCP number without the key.\n\n💾 Save Accounts: \nStore calculated accounts for quick access later.\n\n📤 Easy Sharing:\n    - Copy the RIP code \n    - Generate a QR Code \n    - Share the account through your favorite apps \n\n🔍 RIP Validation: \nCheck the validity of an existing RIP code:\n\n     - By entering it manually \n     - Or scanning a QR Code'**
  String get appInfoContent;
}

class _AppLocalizationsDelegate extends LocalizationsDelegate<AppLocalizations> {
  const _AppLocalizationsDelegate();

  @override
  Future<AppLocalizations> load(Locale locale) {
    return SynchronousFuture<AppLocalizations>(lookupAppLocalizations(locale));
  }

  @override
  bool isSupported(Locale locale) => <String>['ar', 'en', 'fr'].contains(locale.languageCode);

  @override
  bool shouldReload(_AppLocalizationsDelegate old) => false;
}

AppLocalizations lookupAppLocalizations(Locale locale) {


  // Lookup logic when only language code is specified.
  switch (locale.languageCode) {
    case 'ar': return AppLocalizationsAr();
    case 'en': return AppLocalizationsEn();
    case 'fr': return AppLocalizationsFr();
  }

  throw FlutterError(
    'AppLocalizations.delegate failed to load unsupported locale "$locale". This is likely '
    'an issue with the localizations generation tool. Please file an issue '
    'on GitHub with a reproducible sample app and the gen-l10n configuration '
    'that was used.'
  );
}
